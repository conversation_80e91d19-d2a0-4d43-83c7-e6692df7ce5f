# 活跃度倒计时修复总结

## 🔧 修复的问题

### 1. **倒计时不会跳动问题**
**问题原因**: 倒计时计算基于静态的 `data.totalSeconds`，这个值只有在API调用时才更新，不会实时变化。

**解决方案**: 
- 添加实时计算机制，基于活跃度开始时间计算当前总活跃时长
- 修改剩余时间计算方法，使用实时总活跃时长而非静态值

### 2. **活跃度管理器设计不符合全局保活需求**
**问题原因**: 原设计每次进入/离开任务中心都会启动/停止活跃度跟踪，不符合全局保活的需求。

**解决方案**:
- 修改为全局保活机制，只有第一次进入任务中心时才初始化
- 应用前台/后台时暂停/恢复跟踪，而不是完全停止
- 保持活跃度跟踪在整个应用生命周期中持续运行

## 📝 具体修改内容

### 1. ActivityProgressManager 重构

#### 新增属性
```swift
/// 是否已经初始化过（只有第一次进入任务中心时才启动）
private var hasInitialized: Bool = false

/// 活跃度开始时间（用于实时计算总活跃时长）
private var activityStartTime: Date?

/// 服务器返回的基础活跃时长（秒）
private var baseActiveSeconds: Int = 0
```

#### 修改的核心方法

1. **初始化机制**:
   ```swift
   func initializeAndStartTracking() // 只有第一次进入任务中心时调用
   private func startTracking()      // 内部启动方法
   ```

2. **暂停/恢复机制**:
   ```swift
   func pauseTracking()   // 应用进入后台时暂停
   func resumeTracking()  // 应用进入前台时恢复
   ```

3. **实时计算方法**:
   ```swift
   func getCurrentTotalActiveSeconds() -> Int // 基础时长 + 本次会话时长
   private func getCurrentSessionActiveSeconds() -> Int // 当前会话时长
   ```

4. **修正的剩余时间计算**:
   ```swift
   func getCurrentRewardRemainingSeconds() -> Int? {
       let currentTotalSeconds = getCurrentTotalActiveSeconds() // 使用实时值
       let remainingSeconds = reward.conditionValue - currentTotalSeconds
       return max(0, remainingSeconds)
   }
   ```

### 2. 任务中心控制器修改

#### 调用逻辑修正
```swift
// viewWillAppear
ActivityProgressManager.shared.initializeAndStartTracking() // 只初始化一次

// viewWillDisappear  
// 不再停止跟踪，因为它是全局的保活机制
```

#### 调试增强
- 添加实时总活跃时长显示
- 添加实时倒计时测试方法
- 增强日志输出，便于调试

## 🎯 修复后的工作流程

### 1. **初始化流程**
```
第一次进入任务中心 → initializeAndStartTracking() → 开始全局活跃度跟踪
后续进入任务中心 → 跳过初始化 → 继续使用已有的全局跟踪
```

### 2. **应用生命周期处理**
```
应用进入前台 → resumeTracking() → 恢复定时器和活跃度计算
应用进入后台 → pauseTracking() → 暂停定时器，上报累计时长
```

### 3. **实时倒计时计算**
```
基础活跃时长(服务器返回) + 本次会话时长(实时计算) = 当前总活跃时长
当前总活跃时长 → 计算剩余时间 → 更新UI显示
```

### 4. **数据同步机制**
```
API调用成功 → 更新基础活跃时长 → 重置会话开始时间 → 继续实时计算
领取奖励成功 → 同步最新数据 → 更新基础时长 → 显示下一个宝箱倒计时
```

## 🔍 关键技术点

### 1. **实时计算原理**
- **基础时长**: 服务器返回的 `totalSeconds`（上次同步时的累计时长）
- **会话时长**: `Date().timeIntervalSince(activityStartTime)`（本次会话的实时时长）
- **总活跃时长**: 基础时长 + 会话时长

### 2. **时间同步策略**
- 每30秒自动上报累计时长到服务器
- API调用成功后重置会话开始时间
- 应用前台/后台切换时处理时间累计

### 3. **UI更新机制**
- 每秒更新一次UI（定时器）
- 活跃度数据变化时立即更新
- 实时计算确保倒计时准确跳动

## ✅ 预期效果

1. **倒计时实时跳动**: 每秒减少1秒，准确显示剩余时间
2. **全局保活**: 只要应用在前台，活跃度就会持续累计
3. **数据一致性**: 服务器数据与本地实时计算保持同步
4. **性能优化**: 避免频繁API调用，减少网络开销
5. **用户体验**: 倒计时准确，奖励领取及时，状态更新流畅

## 🧪 测试建议

1. **基础功能测试**:
   - 进入任务中心，观察倒计时是否开始跳动
   - 切换应用前台/后台，验证倒计时暂停/恢复
   - 领取奖励后，验证是否正确显示下一个宝箱

2. **边界情况测试**:
   - 多次进入/退出任务中心，验证不会重复初始化
   - 长时间保持应用前台，验证倒计时准确性
   - 网络异常情况下的数据同步

3. **性能测试**:
   - 长时间运行，检查内存泄漏
   - 验证定时器正确清理
   - 检查CPU使用率是否正常
