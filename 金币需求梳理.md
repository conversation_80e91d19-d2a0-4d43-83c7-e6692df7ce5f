# 看视频得金币功能需求分析与实现计划

## 1. 需求概述

### 核心功能
- 用户观看视频时显示金币进度条
- 记录有效观看时长并上报到后端
- 达到条件时自动获得金币奖励
- 支持不同类型作品的差异化处理

### 业务规则
1. **有效时长计算**：只记录实际播放时长，暂停、拖拽、重复观看无效
2. **单视频限制**：每个视频ID只能获得一次奖励
3. **进度保存**：观看进度实时上报并保存
4. **类型区分**：笔记作品不参与金币获取，但保留UI展示

## 2. 现有API分析

### 2.1 已有相关API
```swift
// 上报视频观看进度
func reportVideoWatchProgress(videoId: String, watchSeconds: Int, completion: @escaping (Result<GoldCoinWatchVideoResponse, APIError>) -> Void)

// 查询视频观看进度  
func getVideoWatchProgress(videoId: String, completion: @escaping (Result<GoldCoinVideoProgressResponse, APIError>) -> Void)

// 获取观看视频任务奖励配置
func getTaskWatchVideoRewardConfig(completion: @escaping (Result<GoldCoinTaskWatchVideoRewardConfigResponse, APIError>) -> Void)
```

### 2.2 API返回状态分析
- **上报API返回状态**：
  - 成功：正常记录时长
  - 成功并获得奖励：满足条件自动加金币
  - 失败：视频已录入过（重复观看无效）

## 3. 架构设计

### 3.1 新增管理器：GoldCoinVideoWatchManager
```swift
class GoldCoinVideoWatchManager {
    // 单例模式
    static let shared = GoldCoinVideoWatchManager()
    
    // 核心属性
    private var currentVideoId: String?
    private var accumulatedWatchTime: TimeInterval = 0
    private var lastReportTime: Date?
    private var isWatching: Bool = false
    private var reportTimer: Timer?
    
    // 配置信息
    private var rewardConfig: GoldCoinTaskWatchVideoRewardConfigData?
    
    // 回调
    var onProgressUpdate: ((Double, Int, Int) -> Void)? // 进度, 当前秒数, 总秒数
    var onRewardEarned: ((Int) -> Void)? // 获得金币数量
    var onError: ((String) -> Void)?
}
```

### 3.2 集成到现有架构
- **VideoDisplayCenterViewController**：负责调用管理器的开始/暂停/结束方法
- **GoldCoinView**：显示进度条和金币动画，接收管理器回调更新UI
- **VideoPage**：在播放状态变化时通知管理器

## 4. 实现流程

### 4.1 初始化流程
1. 进入视频播放页面时，获取奖励配置
2. 根据视频ID查询当前进度
3. 初始化进度条显示

### 4.2 观看流程
1. **开始播放**：
   - 记录开始时间
   - 启动定时器（建议5-10秒间隔上报）
   - 开始累计有效时长

2. **暂停播放**：
   - 停止计时器
   - 上报当前累计时长
   - 保存进度状态

3. **拖拽进度条**：
   - 不计入有效时长
   - 重新开始计时

4. **切换视频**：
   - 上报当前视频进度
   - 重置计时器
   - 加载新视频进度

### 4.3 上报策略
- **定时上报**：每10秒上报一次累计时长
- **关键节点上报**：暂停、切换视频、退出页面时立即上报
- **防重复机制**：相同时长不重复上报

## 5. 特殊处理

### 5.1 笔记作品处理
```swift
// 判断作品类型
if videoItem.isNoteType {
    // 显示金币UI但不记录时长
    goldCoinView.showProgressAnimation(fake: true)
    // 不调用管理器的计时方法
} else {
    // 正常视频，启动金币计时
    GoldCoinVideoWatchManager.shared.startWatching(videoId: videoId)
}
```

### 5.2 错误处理
- 网络异常：本地缓存进度，网络恢复后补偿上报
- 重复观看：显示已完成状态，不再计时
- 配置异常：降级到普通播放模式

## 6. UI交互设计

### 6.1 进度条状态
- **未开始**：空进度条
- **进行中**：实时更新进度，显示剩余时间
- **已完成**：满进度条，显示完成状态
- **已失效**：灰色状态，提示重复观看无效

### 6.2 金币动画
- **获得奖励时**：金币飞入动画 + HUD提示
- **笔记作品**：保留转圈动画但不触发奖励

## 7. 潜在问题与建议

### 7.1 现有问题
1. **全局水桶机制冲突**：当前`GoldCoinView`使用全局60秒机制，与单视频进度冲突
2. **状态管理复杂**：需要同时管理全局进度和单视频进度
3. **API调用频率**：过于频繁的上报可能影响性能

### 7.2 建议优化
1. **分离两套机制**：
   - 保留全局水桶用于普通观看奖励
   - 新增单视频进度用于任务奖励
   - UI上可以并存显示

2. **优化上报策略**：
   - 采用批量上报减少网络请求
   - 本地缓存机制防止数据丢失

3. **状态持久化**：
   - 使用Core Data或SQLite存储观看记录
   - 支持离线模式和数据同步

## 8. 实现优先级

### Phase 1（核心功能）
1. 创建`GoldCoinVideoWatchManager`
2. 集成到`VideoDisplayCenterViewController`
3. 实现基础的计时和上报功能

### Phase 2（UI优化）
1. 优化`GoldCoinView`支持双模式
2. 添加进度条和状态显示
3. 实现奖励动画效果

### Phase 3（完善功能）
1. 添加笔记作品特殊处理
2. 实现错误处理和重试机制
3. 性能优化和测试

## 9. 关键代码结构预览

```swift
// 在VideoDisplayCenterViewController中
override func viewDidLoad() {
    super.viewDidLoad()
    setupGoldCoinVideoWatch()
}

private func setupGoldCoinVideoWatch() {
    GoldCoinVideoWatchManager.shared.onProgressUpdate = { [weak self] progress, current, total in
        self?.goldCoinView.updateVideoWatchProgress(progress, current: current, total: total)
    }
    
    GoldCoinVideoWatchManager.shared.onRewardEarned = { [weak self] coins in
        self?.showVideoWatchReward(coins)
    }
}

// 在VideoPageDelegate中
func videoPageDidStartPlaying(_ page: VideoPage) {
    if let videoId = page.videoItem?.id {
        GoldCoinVideoWatchManager.shared.startWatching(videoId: String(videoId))
    }
    // 保留原有全局水桶逻辑
    goldCoinView.startPlaying()
}
```
