# 活跃度宝箱功能实现总结

## 概述

根据用户提供的API文档，我们成功实现了活跃度宝箱功能，将原本的测试倒计时逻辑替换为真实的活跃度宝箱系统。

## 实现的功能

### 1. 数据模型 (ActiveProgressResponse.swift)

创建了完整的活跃度相关数据模型：

- `ActiveRewardConfig`: 活跃度奖励配置模型
- `ActiveProgressData`: 活跃度进度数据模型
- `ActiveProgressResponse`: 查询活跃度状态API响应
- `ReportActiveProgressResponse`: 上报活跃度进度API响应
- `ClaimActiveRewardResponse`: 领取活跃金币API响应
- `NextActivityRewardResponse`: 获取下一个活跃任务奖励API响应
- `ReportActiveProgressRequest`: 活跃度进度上报请求模型

### 2. API路由 (APIRouter.swift)

在`GoldCoin`枚举中添加了4个新的API路由：

- `queryActiveProgress`: 查询用户活跃度状态 (GET)
- `reportActiveProgress(activeSeconds:)`: 上报活跃进度 (POST)
- `claimActiveReward(configId:)`: 领取活跃金币 (POST)
- `getNextActivityReward`: 获取下一个活跃任务奖励 (GET)

### 3. API管理器 (APIManager.swift)

在`APIManager`中添加了4个对应的方法：

- `queryActiveProgress()`: 查询用户活跃度状态
- `reportActiveProgress(activeSeconds:)`: 上报活跃进度
- `claimActiveReward(configId:)`: 领取活跃金币
- `getNextActivityReward()`: 获取下一个活跃任务奖励

每个方法都包含详细的文档说明和错误处理逻辑。

### 4. 活跃度管理器 (ActivityProgressManager.swift)

创建了专门的活跃度管理器，负责：

- **自动跟踪**: 应用前台时自动开始跟踪，后台时停止跟踪
- **定时上报**: 每30秒自动上报累计的活跃时长
- **状态管理**: 维护当前活跃度数据和状态
- **通知机制**: 活跃度更新时发送通知
- **便捷方法**: 提供检查奖励状态、计算剩余时间等便捷方法

主要功能：
- `startTracking()`: 开始跟踪活跃度
- `stopTracking()`: 停止跟踪活跃度
- `reportProgress(seconds:)`: 手动上报活跃度
- `refreshActiveData()`: 刷新活跃度数据
- `hasAvailableRewards()`: 检查是否有可领取奖励
- `getTotalAvailableReward()`: 获取总可领取奖励
- `getNextRewardRemainingSeconds()`: 获取下一个奖励剩余时间

### 5. UI适配 (GoldCoinSystemTaskCenterViewController.swift)

对任务中心UI进行了全面适配：

#### 5.1 数据属性
- 添加了活跃度相关属性
- 添加了初始化状态标记

#### 5.2 生命周期管理
- `viewWillAppear`: 监听活跃度更新通知，开始跟踪
- `viewWillDisappear`: 移除通知监听，停止跟踪

#### 5.3 宝箱按钮逻辑
- **标题显示**: 根据活跃度状态显示不同文案
  - 有可领取奖励: "点击可领 X金币"
  - 倒计时状态: "MM:SS X金币"
  - 加载中: "加载中..."
  - 无奖励: "暂无奖励"

- **点击处理**: 
  - 有可领取奖励时直接领取
  - 未满足条件时显示剩余时间提示

#### 5.4 首个宝箱特殊逻辑
- 检测首个宝箱的`conditionValue`是否为0
- 如果为0，自动上报0秒激活首个宝箱

#### 5.5 实时更新
- 监听活跃度管理器的更新通知
- 自动更新宝箱按钮状态和文案

### 6. 测试功能

添加了调试测试方法：
- 测试查询活跃度状态
- 测试手动上报活跃度
- 测试宝箱状态检查
- 仅在DEBUG模式下启用

## API对接说明

### 1. 查询用户活跃度状态
```
GET /api/video/gold/queryActiveProgress
```
返回用户当前活跃度状态、可领取奖励、下一个奖励配置等。

### 2. 上报活跃进度
```
POST /api/video/gold/reportActiveProgress
Body: {"activeSeconds": 1}
```
向后端上报用户活跃时长，后端会累计并返回最新状态。

### 3. 领取活跃金币
```
POST /api/video/gold/reportActiveProgress/{configId}
```
根据配置ID领取对应的活跃度奖励。

### 4. 获取下一个活跃任务奖励
```
GET /api/video/gold/getNextActivityReward
```
获取下一个可获得的活跃度奖励配置。

## 业务逻辑

### 1. 活跃度累计
- 应用在前台时自动跟踪活跃时长
- 每30秒自动上报累计时长
- 应用进入后台时停止跟踪并上报剩余时长

### 2. 首个宝箱激活
- 首次进入时检查首个宝箱的`conditionValue`
- 如果为0，自动上报0秒激活首个宝箱
- 确保用户进入页面即可看到待领取状态

### 3. 奖励领取
- 优先领取`availableRewards`中的奖励
- 其次检查`nextShortVideoRewardConfig`是否可领取
- 领取成功后自动刷新金币数量和活跃度状态

### 4. 倒计时显示
- 根据`conditionValue - totalSeconds`计算剩余时间
- 实时更新宝箱按钮文案
- 满足条件时自动切换为可领取状态

## 注意事项

1. **测试代码**: 当前包含调试测试代码，正式发布前应移除
2. **旧逻辑**: 原有的倒计时逻辑已注释，可在确认新功能正常后删除
3. **错误处理**: 所有API调用都包含完整的错误处理
4. **性能优化**: 活跃度管理器使用定时器和通知机制，避免频繁API调用

## 下一步

1. 测试所有活跃度相关功能
2. 验证与后端API的正确交互
3. 确认首个宝箱和后续宝箱的不同逻辑
4. 移除调试代码，准备正式发布
