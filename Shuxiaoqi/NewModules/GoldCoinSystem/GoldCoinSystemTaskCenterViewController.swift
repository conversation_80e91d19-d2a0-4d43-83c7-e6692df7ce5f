//
//  GoldCoinSystemTaskCenterViewController.swift
//  Shuxia<PERSON>qi
//
//  Created by yongsheng ye on 2025/8/12.
//

import UIKit
import SnapKit

// MARK: - 金币系统任务中心控制器
class GoldCoinSystemTaskCenterViewController: BaseViewController {

    // MARK: - 数据模型
    private var currentGoldCoins: Int = 0
    private let defaultRewardCoins: Int = 888
    private var dailyEarnableCoins: Int = 2000
    private var exchangeRate: String = "10000金币=1元现金"
    
    // 任务奖励配置数据
    private var taskRewardConfigs: [TaskRewardConfig] = []
    
    // 加载状态
    private var isLoadingTaskConfig: Bool = false

    // 签到数据
    private var checkInDays: [CheckInDay] = []
    private var consecutiveCheckInDays: Int = 2 // 当前连续签到天数（0表示今天还没签到）
    // 今日是否已点击过签到按钮（由外部传入，后续由服务端控制）
    private var hasClickedCheckInButtonToday: Bool = false

    // 观看视频进度
    private var videoWatchProgress: Float = 0.3 // 30%
    private var videoWatchedSeconds: Int = 180 // 已观看3分钟
    private var videoTotalSeconds: Int = 600 // 总共10分钟

    // MARK: - UI 组件

    // 滚动视图
    private lazy var scrollView: UIScrollView = {
        let scrollView = UIScrollView()
        scrollView.backgroundColor = .white
        scrollView.showsVerticalScrollIndicator = false
        
        // 添加下拉刷新
        let refreshControl = UIRefreshControl()
        refreshControl.addTarget(self, action: #selector(handleRefresh(_:)), for: .valueChanged)
        scrollView.refreshControl = refreshControl
        
        return scrollView
    }()

    // 滚动内容容器
    private lazy var scrollContentView: UIView = {
        let view = UIView()
        view.backgroundColor = .white
        return view
    }()

    // 头部背景容器 - #F7F7F7色，20圆角，168高度
    private lazy var headerBackgroundView: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor(hex: "#F7F7F7")
        view.layer.cornerRadius = 20
        return view
    }()

    // 当前金币背景图 - 橙色背景
    private lazy var goldCoinBackgroundView: UIImageView = {
        let imageView = UIImageView()
        imageView.image = UIImage(named: "gold_coin_task_center_header_bg") // 预命名的橙色背景图
        imageView.contentMode = .scaleAspectFill
        imageView.clipsToBounds = true
        imageView.layer.cornerRadius = 16
        imageView.isUserInteractionEnabled = true
        return imageView
    }()

    // 当前金币标题
    private lazy var currentGoldTitleLabel: UILabel = {
        let label = UILabel()
        label.text = "当前金币"
        label.textColor = .white
        label.font = .systemFont(ofSize: 14, weight: .medium)
        return label
    }()

    // 金币数量
    private lazy var goldCoinAmountLabel: UILabel = {
        let label = UILabel()
        label.text = "\(currentGoldCoins)"
        label.textColor = .white
        label.font = .systemFont(ofSize: 24, weight: .bold)
        return label
    }()

    // 提现按钮
    private lazy var withdrawButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setTitle("提现", for: .normal)
        button.setTitleColor(.appThemeOrange, for: .normal)
        button.titleLabel?.font = .systemFont(ofSize: 14, weight: .medium)
        button.backgroundColor = .white
        button.layer.cornerRadius = 16
        button.addTarget(self, action: #selector(withdrawButtonTapped), for: .touchUpInside)
        return button
    }()

    // 今日已赚标签
    private lazy var dailyEarnLabel: UILabel = {
        let label = UILabel()
        label.text = "今日已赚 \(dailyEarnableCoins)金币"
        label.textColor = .white
        label.font = .systemFont(ofSize: 14)
        return label
    }()

    // 兑换比例标签
    private lazy var exchangeRateLabel: UILabel = {
        let label = UILabel()
        label.text = exchangeRate
        label.textColor = UIColor(hex: "#666666")
        label.font = .systemFont(ofSize: 14)
        label.backgroundColor = .clear
        label.textAlignment = .left
        return label
    }()

    // MARK: - 动态任务容器
    
    // 任务容器的垂直堆栈视图
    private lazy var taskStackView: UIStackView = {
        let stackView = UIStackView()
        stackView.axis = .vertical
        stackView.spacing = 0
        stackView.distribution = .fill
        return stackView
    }()
    
    // 存储动态创建的任务视图
    private var taskViews: [TaskItemView] = []
    
    // 签到日历容器（复用现有逻辑）
    private var checkInCalendarView: UIView?
    
    // 视频进度容器（复用现有逻辑）
    private var videoProgressView: UIView?
    
    // 活跃度进度容器
    private var activityProgressView: UIView?

    // 悬浮礼盒按钮（可长按拖动）
    private var giftButton: DraggableGiftButton?
    // 弹窗弱引用，避免重复创建
    private weak var currentGoldPopup: GoldCoinPopupView?
    // 日签弹窗弱引用，避免重复创建
    private weak var currentCheckInPopup: DailyAttendancePopupView?

    // MARK: - 活跃度宝箱相关属性

    // 活跃度数据
    private var activeProgressData: ActiveProgressData?
    // 下一个活跃度奖励配置
    private var nextActiveReward: ActiveRewardConfig?
    // 是否已经初始化过活跃度（首次进入需要上报0秒）
    private var hasInitializedActivity: Bool = false
    // UI更新定时器
    private var uiUpdateTimer: Timer?

    // MARK: - 生命周期
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupData()
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        // 监听活跃度进度更新通知
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleActivityProgressUpdated(_:)),
            name: ActivityProgressManager.activityProgressUpdatedNotification,
            object: nil
        )

        // 初始化并开始跟踪活跃度（只有第一次进入任务中心时才启动）
        ActivityProgressManager.shared.initializeAndStartTracking()

        // 启动UI更新定时器（每秒更新一次倒计时）
        startUIUpdateTimer()
    }

    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        // 移除活跃度进度更新通知
        NotificationCenter.default.removeObserver(
            self,
            name: ActivityProgressManager.activityProgressUpdatedNotification,
            object: nil
        )

        // 不需要停止跟踪活跃度，因为它是全局的保活机制
        // ActivityProgressManager会自动处理应用前台/后台状态

        // 停止UI更新定时器
        stopUIUpdateTimer()
    }

    // MARK: - 签到状态注入（后续由服务器控制）
    func configureCheckIn(consecutiveDays: Int, hasClickedToday: Bool) {
        consecutiveCheckInDays = max(0, min(7, consecutiveDays))
        hasClickedCheckInButtonToday = hasClickedToday
        generateCheckInDays()
    }

    // 兼容旧的测试方法
    func setTestConsecutiveCheckInDays(_ days: Int) {
        configureCheckIn(consecutiveDays: days, hasClickedToday: false)
    }

    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)

        // 初始化悬浮礼盒按钮
        setupGiftButtonIfNeeded()
        
        // 获取金币数据
        fetchGoldCoinData()
        
        // 获取任务奖励配置
        fetchTaskRewardConfig()

        // 获取活跃度数据
        fetchActiveProgressData()

        // 测试活跃度功能（仅用于调试，正式版本应移除）
        #if DEBUG
        DispatchQueue.main.asyncAfter(deadline: .now() + 1) {
            self.testActivityFeatures()
        }
        #endif

    }

    // MARK: - 数据获取方法
    
    private func fetchGoldCoinData() {
        APIManager.shared.getGoldCoinLoadingData { [weak self] result in
            switch result {
            case .success(let response):
                if response.isSuccess, let data = response.data {
                    // 更新当前金币数量
                    self?.currentGoldCoins = data.number
                    DispatchQueue.main.async {
                        self?.goldCoinAmountLabel.text = "\(data.number)"
                    }
                } else {
                    print("获取金币数据失败: \(response.displayMessage)")
                }
            case .failure(let error):
                print("获取金币数据请求失败: \(error.localizedDescription)")
            }
        }
    }
    
    private func fetchTaskRewardConfig() {
        guard !isLoadingTaskConfig else { return }
        
        isLoadingTaskConfig = true
        showLoadingIndicator()
        
        APIManager.shared.getTaskRewardConfig { [weak self] result in
            DispatchQueue.main.async {
                self?.isLoadingTaskConfig = false
                self?.hideLoadingIndicator()
                
                switch result {
                case .success(let response):
                    if response.status == 200 {
                        self?.taskRewardConfigs = response.data
                        self?.updateTaskUIWithConfig()
                    } else {
                        print("获取任务奖励配置失败: \(response.errMsg)")
                        self?.showErrorMessage("获取任务配置失败，请稍后重试")
                    }
                case .failure(let error):
                    print("获取任务奖励配置请求失败: \(error.localizedDescription)")
                    self?.showErrorMessage("网络连接失败，请检查网络后重试")
                }
            }
        }
    }
    
    private func updateTaskUIWithConfig() {
        // 清空现有任务视图
        clearTaskViews()
        
        // 过滤掉活跃度任务（conditionType = 7），它属于右下角宝箱功能
        let displayTasks = taskRewardConfigs.filter { $0.conditionType != 7 }
        
        // 根据API返回的顺序动态创建任务视图
        for (index, config) in displayTasks.enumerated() {
            let taskView = createTaskView(for: config, at: index)
            taskViews.append(taskView)
            taskStackView.addArrangedSubview(taskView)
            
            // 添加分割线（最后一个任务不需要分割线）
            if index < displayTasks.count - 1 {
                let separator = createSeparatorView()
                taskStackView.addArrangedSubview(separator)
            }
        }
        
        // 更新今日已赚金币数量
        updateDailyEarnableCoins()
    }

    // MARK: - 活跃度数据获取方法

    private func fetchActiveProgressData() {
        // 使用活跃度管理器刷新数据
        ActivityProgressManager.shared.refreshActiveData()

        // 获取当前数据并更新UI
        if let data = ActivityProgressManager.shared.getCurrentActiveData() {
            activeProgressData = data
            updateGiftButtonWithActiveData()
        }

        // 首次进入且首个宝箱conditionValue为0时，需要上报0秒激活
        handleFirstTimeActivation()
    }

    private func handleFirstTimeActivation() {
        guard !hasInitializedActivity else { return }

        // 检查当前未领取奖励或下一个奖励配置是否为首个宝箱（conditionValue为0）
        let manager = ActivityProgressManager.shared
        if let currentReward = manager.getCurrentRewardConfig(),
           currentReward.conditionValue == 0 {
            // 使用活跃度管理器上报0秒激活首个宝箱
            print("[TaskCenter] 检测到首个宝箱(conditionValue=0)，上报0秒激活")
            ActivityProgressManager.shared.reportProgress(seconds: 0)
        }

        hasInitializedActivity = true
    }

    private func claimActiveReward(configId: Int) {
        APIManager.shared.claimActiveReward(configId: configId) { [weak self] result in
            DispatchQueue.main.async {
                switch result {
                case .success(let response):
                    if response.isSuccess, let data = response.data {
                        // 更新活跃度数据
                        self?.activeProgressData = data
                        // 同步更新活跃度管理器的数据
                        ActivityProgressManager.shared.updateCurrentActiveData(data)
                        // 更新宝箱按钮UI
                        self?.updateGiftButtonWithActiveData()
                        // 刷新金币数量
                        self?.fetchGoldCoinData()
                        // 显示成功提示
                        self?.showHUD("领取成功！")

                        print("[TaskCenter] 领取成功，已更新到下一个宝箱状态")
                        self?.logCurrentRewardStatus()
                    } else {
                        self?.showHUD(response.displayMessage)
                    }
                case .failure(let error):
                    self?.showHUD("领取失败：\(error.localizedDescription)")
                }
            }
        }
    }

    /// 打印当前奖励状态（用于调试）
    private func logCurrentRewardStatus() {
        let manager = ActivityProgressManager.shared
        if let currentReward = manager.getCurrentRewardConfig() {
            let canClaim = manager.hasAvailableRewards()
            let remaining = manager.getCurrentRewardRemainingSeconds() ?? 0
            let totalActive = manager.getCurrentTotalActiveSeconds()
            print("   - 当前宝箱: ID=\(currentReward.id), 奖励=\(currentReward.rewardValue)金币")
            print("   - 实时总活跃时长: \(totalActive)秒")
            print("   - 可领取: \(canClaim), 剩余时间: \(remaining)秒")
            print("   - 按钮文案: \(getActiveGiftTitle())")
        }
    }
    
    private func clearTaskViews() {
        // 移除所有任务视图
        taskViews.forEach { $0.removeFromSuperview() }
        taskViews.removeAll()
        
        // 清空堆栈视图
        taskStackView.arrangedSubviews.forEach { $0.removeFromSuperview() }
        
        // 清理特殊容器
        checkInCalendarView?.removeFromSuperview()
        checkInCalendarView = nil
        videoProgressView?.removeFromSuperview()
        videoProgressView = nil
        activityProgressView?.removeFromSuperview()
        activityProgressView = nil
    }
    
    private func createTaskView(for config: TaskRewardConfig, at index: Int) -> TaskItemView {
        let taskView = TaskItemView()
        taskView.configure(with: config)
        
        // 设置点击事件
        taskView.onButtonTapped = { [weak self] in
            self?.handleTaskTapped(config: config)
        }
        
        return taskView
    }
    
    private func createSeparatorView() -> UIView {
        let separator = UIView()
        separator.backgroundColor = UIColor(hex: "#E5E5E5")
        
        // 创建一个容器视图来包装分割线
        let containerView = UIView()
        containerView.addSubview(separator)
        
        separator.snp.makeConstraints { make in
            make.height.equalTo(1)
            make.left.right.equalToSuperview().inset(16)
            make.top.bottom.equalToSuperview()
        }
        
        return containerView
    }
    
    private func handleTaskTapped(config: TaskRewardConfig) {
        switch TaskConditionType(rawValue: config.conditionType) {
        case .firstPublishVideo, .publishVideo:
            publishVideoTaskTapped()
        case .continuousSignIn:
            checkInTaskTapped()
        case .inviteFriends:
            inviteFriendsTaskTapped()
        case .videoPlay:
            watchVideoTaskTapped()
        case .activity:
            // 活跃度任务不应该出现在列表中，这里是保护性代码
            print("活跃度任务属于右下角宝箱功能，不应在任务列表中显示")
        default:
            print("未知任务类型: \(config.conditionType)")
        }
    }
    
    private func updateDailyEarnableCoins() {
        // 计算今日已赚的金币数量（未完成任务的奖励总和，排除活跃度任务）
        let incompleteTaskRewards = taskRewardConfigs
            .filter { $0.conditionType != 7 && TaskState(rawValue: $0.state) == .incomplete }
            .map { $0.rewardValue }
            .reduce(0, +)
        
        if incompleteTaskRewards > 0 {
            dailyEarnableCoins = incompleteTaskRewards
            dailyEarnLabel.text = "今日已赚 \(dailyEarnableCoins)金币"
        }
    }
    
    private func activityTaskTapped() {
        print("活跃度任务被点击")
        // TODO: 跳转到活跃度页面
        let alert = UIAlertController(title: "活跃度", message: "跳转到活跃度页面", preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }
    
    private func updateSignInCalendar(signRewardList: [SignRewardConfig], currentDay: Int) {
        // 清空现有数据
        checkInDays = []
        
        // 根据API返回的签到奖励配置更新签到日历
        for (index, signConfig) in signRewardList.enumerated() {
            let dayName = "第\(index + 1)天"
            let reward = signConfig.rewardValue
            // signConfig.state: 0-未签到 1-已签到
            let isCheckedIn = (signConfig.state == 1)
            checkInDays.append(CheckInDay(day: dayName, reward: reward, isCheckedIn: isCheckedIn))
        }
        
        // 更新当前连续签到天数
        consecutiveCheckInDays = currentDay
    }

    // MARK: - 设置方法

    private func setupUI() {
        navTitle = "任务中心"
        contentView.backgroundColor = .white

        // 添加滚动视图
        contentView.addSubview(scrollView)
        scrollView.addSubview(scrollContentView)

        // 添加头部背景容器
        scrollContentView.addSubview(headerBackgroundView)

        // 添加当前金币背景图到头部背景容器
        headerBackgroundView.addSubview(goldCoinBackgroundView)
        goldCoinBackgroundView.addSubview(currentGoldTitleLabel)
        goldCoinBackgroundView.addSubview(goldCoinAmountLabel)
        goldCoinBackgroundView.addSubview(withdrawButton)
        goldCoinBackgroundView.addSubview(dailyEarnLabel)

        // 添加兑换比例标签到头部背景容器
        headerBackgroundView.addSubview(exchangeRateLabel)

        // 添加动态任务容器
        scrollContentView.addSubview(taskStackView)

        setupConstraints()
        setupData() // 先设置数据
    }

    private func setupData() {
        // 根据当前连续签到天数生成签到数据
        generateCheckInDays()
    }

    private func generateCheckInDays() {
        checkInDays = []
        // 固定展示 第1天 ~ 第7天；连续到第N天就亮前N天
        for i in 1...7 {
            let dayName = "第\(i)天"
            let reward = i * 10 // +10, +20, ... +70
            let isCheckedIn = i <= consecutiveCheckInDays
            checkInDays.append(CheckInDay(day: dayName, reward: reward, isCheckedIn: isCheckedIn))
        }
    }

    private func setupConstraints() {
        // 滚动视图约束
        scrollView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        scrollContentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }

        // 头部背景容器约束 - 距离顶部16pt，左右各16pt，高度168pt
        headerBackgroundView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(16)
            make.left.right.equalToSuperview().inset(16)
            make.height.equalTo(168)
        }

        // 当前金币背景图约束 - 在头部背景容器上方
        goldCoinBackgroundView.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.height.equalTo(120) // 调整高度，为下方文案留空间
        }

        // 当前金币标题约束
        currentGoldTitleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(16)
            make.left.equalToSuperview().offset(16)
            make.height.equalTo(20)
        }

        // 金币数量约束
        goldCoinAmountLabel.snp.makeConstraints { make in
            make.top.equalTo(currentGoldTitleLabel.snp.bottom).offset(8)
            make.left.equalTo(currentGoldTitleLabel)
        }

        // 提现按钮约束
        withdrawButton.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(20)
            make.right.equalToSuperview().offset(-20)
            make.width.equalTo(74)
            make.height.equalTo(32)
        }

        // 今日已赚标签约束
        dailyEarnLabel.snp.makeConstraints { make in
            make.bottom.equalToSuperview().offset(-16)
            make.left.equalTo(currentGoldTitleLabel)
        }

        // 兑换比例标签约束 - 在当前金币背景图下方+8pt，左边距离背景View +16pt
        exchangeRateLabel.snp.makeConstraints { make in
            make.top.equalTo(goldCoinBackgroundView.snp.bottom).offset(8)
            make.left.equalToSuperview().offset(16)
            make.bottom.equalToSuperview().offset(-16)
        }

        // 动态任务容器约束
        taskStackView.snp.makeConstraints { make in
            make.top.equalTo(headerBackgroundView.snp.bottom).offset(16)
            make.left.right.equalToSuperview()
            make.bottom.equalToSuperview().offset(-20)
        }
    }


    // 初始化并添加悬浮礼盒按钮
    private func setupGiftButtonIfNeeded() {
        // 已添加则不重复添加
        if let button = giftButton, button.superview != nil {
            contentView.bringSubviewToFront(button)
            return
        }

        let button = DraggableGiftButton()
        // 初始化文案
        button.setTitle(initialGiftTitle())
        button.addTarget(self, action: #selector(giftButtonTapped), for: .touchUpInside)

        contentView.addSubview(button)

        // 动态尺寸与初始位置（靠右下，留出一定边距；宽度按标题自适应）
        let defaultSize = button.intrinsicContentSize
        let height: CGFloat = defaultSize.height.isFinite ? defaultSize.height : 120
        let inset: CGFloat = 16
        let maxWidth = max(120, contentView.bounds.width - inset * 2)
        let width: CGFloat = button.preferredWidth(maxWidth: maxWidth)
        let x = contentView.bounds.width - inset - width
        let y = max(inset, contentView.bounds.height - height - 100)
        button.frame = CGRect(x: x, y: y, width: width, height: height)

        giftButton = button
        contentView.bringSubviewToFront(button)
    }

    @objc private func giftButtonTapped() {
        let manager = ActivityProgressManager.shared
        guard let currentReward = manager.getCurrentRewardConfig() else {
            showHUD("暂无奖励配置")
            return
        }

        print("礼盒按钮被点击 -> 弹出活跃度宝箱弹窗")
        presentActiveRewardPopup(rewardConfig: currentReward)
    }

    /// 弹出活跃度奖励弹窗
    private func presentActiveRewardPopup(rewardConfig: ActiveRewardConfig) {
        // 若已存在，先移除旧的
        currentGoldPopup?.dismiss(animated: false)

        let manager = ActivityProgressManager.shared
        let canClaim = manager.hasAvailableRewards()
        let remainingSeconds = manager.getCurrentRewardRemainingSeconds() ?? 0

        let popup = GoldCoinPopupView()
        popup.onDismiss = { [weak self] in
            self?.currentGoldPopup = nil
        }

        if canClaim {
            // 可领取状态 - 显示领取弹窗
            popup.configure(mode: .reward,
                          valueText: "\(rewardConfig.rewardValue)金币",
                          subtitleText: "恭喜获得奖励！")
            popup.onConfirm = { [weak self] in
                self?.handleActiveRewardClaim(configId: rewardConfig.id)
            }
        } else {
            // 倒计时状态 - 显示倒计时弹窗
            popup.configure(mode: .countdown,
                          valueText: formatSeconds(remainingSeconds),
                          subtitleText: "即可获得\(rewardConfig.rewardValue)金币！")
            popup.onConfirm = { [weak self] in
                // 倒计时状态下点击不做任何操作
                self?.showHUD("还需要活跃 \(self?.formatSeconds(remainingSeconds) ?? "0秒") 才能领取奖励")
            }
        }

        popup.show(in: self.view)
        currentGoldPopup = popup
    }

    /// 处理活跃度奖励领取
    private func handleActiveRewardClaim(configId: Int) {
        // 先关闭弹窗
        currentGoldPopup?.dismiss(animated: true)

        // 调用领取API
        claimActiveReward(configId: configId)
    }

    // 展示金币弹窗（左右24pt，按327:436动态高度）
    private func presentGoldCoinPopup() {
        // 若已存在，先移除旧的
        currentGoldPopup?.dismiss(animated: false)

        // 达到每日上限：不弹窗，直接 HUD
        let mgr = GoldCountdownManager.shared
        if mgr.isDailyLimitReached {
            updateGiftButtonTitle("明天再来")
            showHUD("今日领取数量已达上限，请明日再来~")
            return
        }

        let popup = GoldCoinPopupView()
        popup.onDismiss = { [weak self] in
            self?.currentGoldPopup = nil
        }
        // 根据当前状态配置
        if mgr.isRunning {
            let reward = mgr.rewardCoins > 0 ? mgr.rewardCoins : defaultRewardCoins
            popup.configure(mode: .countdown, valueText: formatSeconds(mgr.remainingSeconds), subtitleText: "即可获得\(reward)金币！")
        } else {
            let reward = currentRewardCoins()
            popup.configure(mode: .reward, valueText: "\(reward)金币")
        }
        popup.onConfirm = { [weak self] in
            self?.handlePopupConfirm()
        }
        popup.show(in: self.view)
        currentGoldPopup = popup
    }

    // 对外提供：更新礼盒按钮文案
    func updateGiftButtonTitle(_ text: String) {
        guard let button = giftButton else { return }
        button.setTitle(text)
        // 标题变化后同步调整宽度，保持在父视图边界内
        let inset: CGFloat = 8
        let maxWidth = max(120, contentView.bounds.width - inset * 2)
        let newWidth = button.preferredWidth(maxWidth: maxWidth)
        var frame = button.frame
        let oldCenter = button.center
        frame.size.width = newWidth
        button.frame = frame
        // 约束中心点不越界（水平）
        let halfW = newWidth * 0.5
        let minX = inset + halfW
        let maxX = contentView.bounds.width - inset - halfW
        let clampedX = min(max(oldCenter.x, minX), maxX)
        button.center = CGPoint(x: clampedX, y: oldCenter.y)
    }

    // MARK: - 活跃度宝箱逻辑
    private func initialGiftTitle() -> String {
        return getActiveGiftTitle()
    }

    private func getActiveGiftTitle() -> String {
        let manager = ActivityProgressManager.shared

        // 获取当前宝箱配置
        guard let currentReward = manager.getCurrentRewardConfig() else {
            return activeProgressData == nil ? "加载中..." : "明天再来"
        }

        // 检查是否有可领取的奖励
        if manager.hasAvailableRewards() {
            let rewardValue = manager.getCurrentAvailableReward()
            return "点击查看 \(rewardValue)金币"
        }

        // 显示倒计时
        if let remainingSeconds = manager.getCurrentRewardRemainingSeconds(), remainingSeconds > 0 {
            return "\(formatSeconds(remainingSeconds)) \(currentReward.rewardValue)金币"
        }

        return "点击查看 \(currentReward.rewardValue)金币"
    }

    private func updateGiftButtonWithActiveData() {
        let title = getActiveGiftTitle()
        updateGiftButtonTitle(title)
    }

    // MARK: - UI更新定时器

    /// 启动UI更新定时器
    private func startUIUpdateTimer() {
        stopUIUpdateTimer() // 先停止之前的定时器

        uiUpdateTimer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { [weak self] _ in
            // 每秒更新一次UI，主要用于倒计时显示
            self?.updateGiftButtonWithActiveData()
        }
    }

    /// 停止UI更新定时器
    private func stopUIUpdateTimer() {
        uiUpdateTimer?.invalidate()
        uiUpdateTimer = nil
    }

    // MARK: - 活跃度进度更新处理

    @objc private func handleActivityProgressUpdated(_ notification: Notification) {
        // 从通知中获取更新的活跃度数据
        if let data = notification.userInfo?["data"] as? ActiveProgressData {
            activeProgressData = data
            updateGiftButtonWithActiveData()
            print("[TaskCenter] 活跃度数据已更新: 总时长=\(data.totalSeconds)秒")

            // 打印当前状态用于调试
            logCurrentRewardStatus()
        }
    }

    // MARK: - 测试方法（用于调试）

    /// 测试活跃度功能（仅用于调试）
    func testActivityFeatures() {
        print("[TaskCenter] 开始测试活跃度功能...")

        // 1. 测试查询活跃度状态
        print("[TaskCenter] 1. 测试查询活跃度状态")
        ActivityProgressManager.shared.refreshActiveData()

        // 2. 测试手动上报活跃度
        DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
            print("[TaskCenter] 2. 测试手动上报活跃度（1秒）")
            ActivityProgressManager.shared.reportProgress(seconds: 1)
        }

        // 3. 测试宝箱状态检查
        DispatchQueue.main.asyncAfter(deadline: .now() + 4) {
            print("[TaskCenter] 3. 测试宝箱状态检查")
            let manager = ActivityProgressManager.shared
            print("   - 实时总活跃时长: \(manager.getCurrentTotalActiveSeconds())秒")
            print("   - 是否有可领取奖励: \(manager.hasAvailableRewards())")
            print("   - 当前可领取奖励: \(manager.getCurrentAvailableReward())金币")
            if let remaining = manager.getCurrentRewardRemainingSeconds() {
                print("   - 当前宝箱剩余时间: \(remaining)秒")
            }
            if let currentReward = manager.getCurrentRewardConfig() {
                print("   - 当前宝箱配置: ID=\(currentReward.id), 条件值=\(currentReward.conditionValue), 奖励=\(currentReward.rewardValue)")
            }
            print("[TaskCenter] ✅ 活跃度宝箱逻辑修正完成")

            // 4. 测试实时倒计时（每5秒打印一次状态）
            self?.testRealTimeCountdown()
        }
    }

    /// 测试实时倒计时功能
    private func testRealTimeCountdown() {
        var testCount = 0
        Timer.scheduledTimer(withTimeInterval: 5.0, repeats: true) { timer in
            testCount += 1
            let manager = ActivityProgressManager.shared
            let totalActive = manager.getCurrentTotalActiveSeconds()
            let remaining = manager.getCurrentRewardRemainingSeconds() ?? 0
            let buttonText = self.getActiveGiftTitle()

            print("[倒计时测试 \(testCount)] 总活跃: \(totalActive)秒, 剩余: \(remaining)秒, 按钮: \(buttonText)")

            // 测试10次后停止
            if testCount >= 10 {
                timer.invalidate()
                print("[倒计时测试] 测试完成")
            }
        }
    }

    private func currentRewardCoins() -> Int {
        let mgr = GoldCountdownManager.shared
        return mgr.rewardCoins > 0 ? mgr.rewardCoins : defaultRewardCoins
    }

    private func formatSeconds(_ seconds: Int) -> String {
        let m = seconds / 60
        let s = seconds % 60
        return String(format: "%02d:%02d", m, s)
    }

    // MARK: - 旧的倒计时逻辑（已废弃，现在使用活跃度逻辑）
    /*
    @objc private func handleCountdownTick(_ noti: Notification) {
        let mgr = GoldCountdownManager.shared
        if mgr.isDailyLimitReached {
            // 达到上限后不再展示倒计时文案
            updateGiftButtonTitle("明天再来")
            return
        }
        let reward = (noti.userInfo?["reward"] as? Int) ?? currentRewardCoins()
        let remain = mgr.remainingSeconds
        // 更新礼盒
        updateGiftButtonTitle("\(formatSeconds(remain)) \(reward)金币")
        // 若弹窗存在且是倒计时态，则同步时间
        if let popup = currentGoldPopup {
            popup.configure(mode: .countdown, valueText: formatSeconds(remain), subtitleText: "即可获得\(reward)金币！")
        }
    }
    */

    /*
    @objc private func handleCountdownFinished(_ noti: Notification) {
        let mgr = GoldCountdownManager.shared
        if mgr.isDailyLimitReached {
            updateGiftButtonTitle("明天再来")
            return
        }
        let reward = (noti.userInfo?["reward"] as? Int) ?? currentRewardCoins()
        // 更新礼盒文案为可领取
        updateGiftButtonTitle("点击可领 \(reward)金币")
        // 若弹窗还在，切换为领取态（不收起弹窗）
        if let popup = currentGoldPopup {
            popup.configure(mode: .reward, valueText: "\(reward)金币")
        }
    }
    */

    private func handlePopupConfirm() {
        let mgr = GoldCountdownManager.shared
        if mgr.isRunning {
            // 倒计时态下点击：不做领取
            return
        }
        // 达到每日上限：仅提示
        if mgr.isDailyLimitReached {
            showHUD("今日领取数量已达上限，请明日再来~")
            return
        }
        // 领取逻辑：加金币
        let reward = currentRewardCoins()
        currentGoldCoins += reward
        goldCoinAmountLabel.text = "\(currentGoldCoins)"
        // 领取计数 +1
        let count = mgr.incrementDailyClaim()
        // 若已达上限：不再开启倒计时，按钮改为“明天再来”
        if count >= mgr.dailyLimit {
            updateGiftButtonTitle("明天再来")
        } else {
            // 开始全局30s倒计时
            mgr.start(duration: 30, reward: reward)
            // 更新礼盒为倒计时文案
            updateGiftButtonTitle("00:30 \(reward)金币")
        }
    }

    // MARK: - Loading Indicator
    private var loadingIndicator: UIActivityIndicatorView?
    
    private func showLoadingIndicator() {
        guard loadingIndicator == nil else { return }
        
        let indicator = UIActivityIndicatorView(style: .medium)
        indicator.color = .appThemeOrange
        indicator.translatesAutoresizingMaskIntoConstraints = false
        
        view.addSubview(indicator)
        NSLayoutConstraint.activate([
            indicator.centerXAnchor.constraint(equalTo: view.centerXAnchor),
            indicator.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor, constant: 100)
        ])
        
        indicator.startAnimating()
        loadingIndicator = indicator
    }
    
    private func hideLoadingIndicator() {
        loadingIndicator?.stopAnimating()
        loadingIndicator?.removeFromSuperview()
        loadingIndicator = nil
    }
    
    // MARK: - Refresh Control
    @objc private func handleRefresh(_ refreshControl: UIRefreshControl) {
        // 刷新数据
        fetchGoldCoinData()
        fetchTaskRewardConfig()
        
        // 延迟停止刷新动画，等待API请求完成
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            refreshControl.endRefreshing()
        }
    }
    
    // MARK: - Public Methods
    /// 手动刷新任务配置
    func refreshTaskConfig() {
        fetchTaskRewardConfig()
    }
    
    // MARK: - Error Handling
    private func showErrorMessage(_ message: String) {
        showHUD(message, duration: 3.0)
    }
    
    // MARK: - HUD / Toast
    private func showHUD(_ message: String, duration: TimeInterval = 2.0) {
        DispatchQueue.main.async {
            let container = UIView()
            container.backgroundColor = UIColor.black.withAlphaComponent(0.8)
            container.layer.cornerRadius = 8
            container.clipsToBounds = true
            container.alpha = 0

            let label = UILabel()
            label.textColor = .white
            label.font = .systemFont(ofSize: 14)
            label.numberOfLines = 0
            label.textAlignment = .center
            label.text = message
            label.translatesAutoresizingMaskIntoConstraints = false
            container.addSubview(label)

            NSLayoutConstraint.activate([
                label.topAnchor.constraint(equalTo: container.topAnchor, constant: 12),
                label.bottomAnchor.constraint(equalTo: container.bottomAnchor, constant: -12),
                label.leadingAnchor.constraint(equalTo: container.leadingAnchor, constant: 16),
                label.trailingAnchor.constraint(equalTo: container.trailingAnchor, constant: -16)
            ])

            guard let window = UIApplication.shared.windows.first(where: { $0.isKeyWindow }) else { return }
            window.addSubview(container)
            container.translatesAutoresizingMaskIntoConstraints = false
            NSLayoutConstraint.activate([
                container.centerXAnchor.constraint(equalTo: window.centerXAnchor),
                container.centerYAnchor.constraint(equalTo: window.centerYAnchor),
                container.widthAnchor.constraint(lessThanOrEqualTo: window.widthAnchor, multiplier: 0.8)
            ])

            UIView.animate(withDuration: 0.25, animations: { container.alpha = 1 }) { _ in
                DispatchQueue.main.asyncAfter(deadline: .now() + duration) {
                    UIView.animate(withDuration: 0.25, animations: { container.alpha = 0 }) { _ in
                        container.removeFromSuperview()
                    }
                }
            }
        }
    }

    // MARK: - 事件处理

    @objc private func withdrawButtonTapped() {
        print("提现按钮被点击")
        // 跳转到提现页面
        let withdrawalVC = GoldCoinWithdrawalCenterViewController()
        navigationController?.pushViewController(withdrawalVC, animated: true)
    }

    private func publishVideoTaskTapped() {
        print("发布视频任务被点击")
        let vc = VideoRecordViewController()
        navigationController?.pushViewController(vc, animated: true)
    }

    private func checkInTaskTapped() {
        print("签到任务被点击")
        // 新逻辑：点击仅弹窗确认，确认后才执行签到
        presentCheckInPopup()
    }

    private func inviteFriendsTaskTapped() {
        print("邀请好友任务被点击")
        // TODO: 跳转到邀请好友页面
        let alert = UIAlertController(title: "邀请好友", message: "跳转到邀请好友页面", preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定", style: .default))
        present(alert, animated: true)
    }

    private func watchVideoTaskTapped() {
        print("观看视频任务被点击")

        // 显示加载状态
        showHUD("正在获取视频...")

        // 使用首页推荐接口获取视频列表
        APIManager.shared.getMainWorksInfo { [weak self] result in
            DispatchQueue.main.async {
                self?.hideLoadingIndicator()

                switch result {
                case .success(let response):
                    if response.isSuccess, let worksGroups = response.data, !worksGroups.isEmpty {
                        // 从所有分组中收集所有视频
                        var allVideos: [VideoItem] = []
                        for group in worksGroups {
                            if let videos = group.list {
                                allVideos.append(contentsOf: videos)
                            }
                        }

                        if !allVideos.isEmpty {
                            // 随机选择一个视频进行播放
                            let randomIndex = Int.random(in: 0..<allVideos.count)
                            let selectedVideo = allVideos[randomIndex]
                            print("随机选择视频: ID=\(selectedVideo.id ?? 0), 标题=\(selectedVideo.worksTitle ?? "无标题"), 总视频数=\(allVideos.count)")

                            // 跳转到视频播放页面，使用正常浏览模式
                            self?.navigateToVideoDisplay(with: selectedVideo, videoList: allVideos, startIndex: randomIndex)
                        } else {
                            print("获取到的视频列表为空")
                            self?.showErrorMessage("暂无可用视频，请稍后重试")
                        }
                    } else {
                        print("获取首页推荐失败: \(response.displayMessage)")
                        self?.showErrorMessage("获取视频失败，请稍后重试")
                    }
                case .failure(let error):
                    print("获取首页推荐请求失败: \(error.localizedDescription)")
                    self?.showErrorMessage("网络连接失败，请检查网络后重试")
                }
            }
        }
    }

    /// 跳转到视频播放页面
    private func navigateToVideoDisplay(with videoItem: VideoItem, videoList: [VideoItem]? = nil, startIndex: Int = 0) {
        let videoDisplayVC: VideoDisplayCenterViewController

        if let list = videoList, !list.isEmpty {
            // 使用正常浏览模式，传入完整视频列表
            videoDisplayVC = VideoDisplayCenterViewController(
                videoList: list,
                startIndex: startIndex,
                hideNavBackButton: false,
                showCustomNavBar: true,
                needsTabBarOffset: false
            )
            print("跳转到视频播放页面（正常浏览模式）: 起始索引=\(startIndex), 总视频数=\(list.count)")
        } else {
            // 降级到单视频模式
            videoDisplayVC = VideoDisplayCenterViewController(
                singleVideoItem: videoItem,
                hideNavBackButton: false,
                showCustomNavBar: true,
                needsTabBarOffset: false
            )
            print("跳转到视频播放页面（单视频模式）: \(videoItem.worksTitle ?? "无标题")")
        }

        // 跳转到视频播放页面
        navigationController?.pushViewController(videoDisplayVC, animated: true)
    }

    // 自定义签到弹窗（先实现背景与关闭按钮，内容后续补充）
    private func presentCheckInPopup() {
        // 若已存在先移除
        currentCheckInPopup?.dismiss(animated: false)

        let popup = DailyAttendancePopupView()
        // 示例文案 + 动态已连签天数
        popup.configure(
            title: "连续签到7天得18888金币",
            subtitle: "已连签\(consecutiveCheckInDays)/7天，断签将无法得到最终大奖"
        )
        popup.onDismiss = { [weak self] in
            self?.currentCheckInPopup = nil
        }
        popup.onConfirm = { [weak self, weak popup] in
            self?.performCheckIn()
            popup?.dismiss(animated: true)
        }
        // 计算今天应该签到的天数（已签到天数+1）
        let signedDaysCount = checkInDays.filter { $0.isCheckedIn }.count
        let todayIndex = signedDaysCount // 今天要签到的索引（0-based）
        
        // 展示今日可领金币数
        var todayReward = 0
        if todayIndex < checkInDays.count {
            todayReward = checkInDays[todayIndex].reward
        }
        popup.setCoinAmount(todayReward)
        
        // 设置签到进度（当前应签到的索引 + 每日奖励文案）
        let amounts: [Int] = (0..<7).map { i in
            return (i < checkInDays.count) ? checkInDays[i].reward : 0
        }
        popup.setProgress(currentIndex: todayIndex, amounts: amounts)
        popup.show(in: self.view)
        currentCheckInPopup = popup
    }

    private func performCheckIn() {
        // 调用签到API
        APIManager.shared.userSign { [weak self] result in
            DispatchQueue.main.async {
                switch result {
                case .success(let response):
                    if response.status == 200 {
                        // 签到成功，刷新任务配置以获取最新状态
                        self?.fetchTaskRewardConfig()
                        self?.fetchGoldCoinData()
                        self?.showHUD("签到成功！")
                    } else {
                        self?.showHUD(response.errMsg ?? "签到失败")
                    }
                case .failure(let error):
                    self?.showHUD("签到失败：\(error.localizedDescription)")
                }
            }
        }
    }

    // MARK: - 内存管理

    deinit {
        // 清理定时器
        stopUIUpdateTimer()
        // 移除通知监听
        NotificationCenter.default.removeObserver(self)
        print("[TaskCenter] 任务中心控制器已释放")
    }
}

// MARK: - 数据模型

struct CheckInDay {
    let day: String
    let reward: Int
    var isCheckedIn: Bool
}
