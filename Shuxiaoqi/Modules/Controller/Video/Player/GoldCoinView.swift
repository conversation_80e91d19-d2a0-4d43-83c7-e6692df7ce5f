//
//  GoldCoinView.swift
//  Shuxiaoqi
//
//  Created by yongsheng ye on 2025/8/11.
//

import UIKit
import SnapKit

// MARK: - 本地存储管理器（全局水桶机制）
class GoldCoinStorageManager {
    static let shared = GoldCoinStorageManager()
    private let userDefaults = UserDefaults.standard
    private let globalWatchTimeKey = "GoldCoin_GlobalWatchTime"

    private init() {}

    /// 获取全局累计观看时间
    func getGlobalWatchTime() -> TimeInterval {
        return userDefaults.double(forKey: globalWatchTimeKey)
    }

    /// 保存全局累计观看时间
    func saveGlobalWatchTime(_ time: TimeInterval) {
        userDefaults.set(time, forKey: globalWatchTimeKey)
        print("[GoldCoinStorage] 保存全局观看时间: \(time)s")
    }

    /// 重置全局观看时间（获得金币后）
    func resetGlobalWatchTime() {
        userDefaults.set(0.0, forKey: globalWatchTimeKey)
        print("[GoldCoinStorage] 重置全局观看时间")
    }

    /// 增加观看时间
    func addWatchTime(_ deltaTime: TimeInterval) -> TimeInterval {
        let currentTime = getGlobalWatchTime()
        let newTime = currentTime + deltaTime
        saveGlobalWatchTime(newTime)
        return newTime
    }

    /// 清除所有记录（测试用）
    func clearAllRecords() {
        userDefaults.removeObject(forKey: globalWatchTimeKey)
    }
}

/// 金币控件
/// 40*40大小，透明黑灰色圆形背景，中间30*30金币图片，外围#E9D65A色进度条
/// 支持转圈动画（2秒一圈）和外部控制显示/隐藏进度条
class GoldCoinView: UIView {
    
    // MARK: - UI Components
    private let backgroundView = UIView()
    private let coinImageView = UIImageView()
    private let progressLayer = CAShapeLayer()
    private var rotationAnimation: CABasicAnimation?
    
    // MARK: - Properties
    private var isAnimating = false
    private var shouldShowProgress = true
    private var isLoggedIn = false
    private var isPlaying = false
    private var lastUpdateTime: Date?
    private var goldCoinTimer: Timer?

    // 全局任务水桶机制（通过配置API获取）
    private var globalTaskCurrentSeconds: Int = 0      // 当前进度（从服务器获取）
    private var globalTaskTotalSeconds: Int = 120      // 总需要秒数（从配置获取）
    private var globalTaskProgress: Double = 0.0       // 进度百分比
    private var hasGlobalTaskConfig: Bool = false      // 是否已加载配置

    // 本地累计时间（用于实时更新）
    private var localAccumulatedTime: TimeInterval = 0

    // 回调
    var onGoldCoinEarned: (() -> Void)?
    
    // MARK: - Initialization
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupUI()
    }
    
    // MARK: - Setup
    private func setupUI() {
        // 设置背景圆形视图
        backgroundView.backgroundColor = UIColor.black.withAlphaComponent(0.3)
        backgroundView.layer.cornerRadius = 20 // 40*40的一半
        addSubview(backgroundView)
        
        // 设置金币图片
        coinImageView.image = UIImage(named: "video_gold_coin")
        coinImageView.contentMode = .scaleAspectFit
        addSubview(coinImageView)
        
        // 设置进度条
        setupProgressLayer()
        
        // 设置约束
        setupConstraints()
    }
    
    private func setupProgressLayer() {
        // 创建圆形路径
        let center = CGPoint(x: 20, y: 20) // 40*40的中心点
        let radius: CGFloat = 19 // 半径稍小于背景圆形
        let startAngle = -CGFloat.pi / 2 // 从顶部开始
        let endAngle = startAngle + 2 * CGFloat.pi // 完整圆形
        
        let circularPath = UIBezierPath(arcCenter: center,
                                       radius: radius,
                                       startAngle: startAngle,
                                       endAngle: endAngle,
                                       clockwise: true)
        
        progressLayer.path = circularPath.cgPath
        progressLayer.strokeColor = UIColor(hex: "#E9D65A").cgColor
        progressLayer.lineWidth = 2.0
        progressLayer.fillColor = UIColor.clear.cgColor
        progressLayer.lineCap = .round
        progressLayer.strokeStart = 0
        progressLayer.strokeEnd = 0
        
        layer.addSublayer(progressLayer)
    }
    
    private func setupConstraints() {
        backgroundView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        coinImageView.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.size.equalTo(30)
        }
    }
    
    // MARK: - Public Methods

    /// 设置登录状态
    func setupGoldCoin(isUserLoggedIn: Bool) {
        self.isLoggedIn = isUserLoggedIn
        isHidden = !isUserLoggedIn

        // 如果已经在播放中且有全局任务配置，不要停止计时器
        let shouldKeepTimer = isPlaying && hasGlobalTaskConfig

        if !shouldKeepTimer {
            // 停止之前的计时器
            stopGoldCoinTimer()
        }

        if isLoggedIn {
            // 已登录：显示当前任务进度
            updateProgressDisplay()
        } else {
            // 未登录：简单的2秒动画
            startUnloggedAnimation()
        }

        print("[GoldCoin] 设置金币控件 - 已登录: \(isUserLoggedIn), 保持计时器: \(shouldKeepTimer)")
    }

    /// 开始播放（已登录用户开始计时）
    /// 注意：现在计时由GoldCoinVideoWatchManager控制，这里只做UI更新
    func startPlaying() {
        guard isLoggedIn else { return }

        isPlaying = true
        lastUpdateTime = Date()

        // 只有在有全局任务配置时才启动计时器
        if hasGlobalTaskConfig {
            startGoldCoinTimer()
        }

        print("[GoldCoin] 开始播放计时（受上报管理器控制）")
    }

    /// 暂停播放
    func pausePlaying() {
        guard isLoggedIn else { return }

        isPlaying = false
        updateLocalAccumulatedTime()
        stopGoldCoinTimer()

        print("[GoldCoin] 暂停播放，本地累计时间: \(localAccumulatedTime)s")
    }

    /// 拖动进度条时调用（不影响全局时长，只是暂停/恢复计时）
    func seekTo(time: TimeInterval) {
        guard isLoggedIn else { return }

        // 拖动时更新本地累计时长
        if isPlaying {
            updateLocalAccumulatedTime()
            lastUpdateTime = Date()
        }

        print("[GoldCoin] 拖动进度条，本地累计时间: \(localAccumulatedTime)s")
    }

    /// 未登录用户的简单动画
    private func startUnloggedAnimation() {
        guard shouldShowProgress else { return }

        progressLayer.removeAllAnimations()
        progressLayer.strokeEnd = 0
        progressLayer.isHidden = false
        isAnimating = true

        let animation = CABasicAnimation(keyPath: "strokeEnd")
        animation.fromValue = 0
        animation.toValue = 1
        animation.duration = 2.0
        animation.timingFunction = CAMediaTimingFunction(name: .linear)
        animation.delegate = self

        progressLayer.add(animation, forKey: "unloggedAnimation")
        progressLayer.strokeEnd = 1
    }

    /// 开始金币计时器（已登录用户）
    private func startGoldCoinTimer() {
        stopGoldCoinTimer()

        goldCoinTimer = Timer.scheduledTimer(withTimeInterval: 0.1, repeats: true) { [weak self] _ in
            self?.updateGoldCoinProgress()
        }
    }

    /// 停止金币计时器
    private func stopGoldCoinTimer() {
        goldCoinTimer?.invalidate()
        goldCoinTimer = nil
    }

    /// 更新金币进度（每0.1秒调用一次）
    private func updateGoldCoinProgress() {
        guard isLoggedIn, isPlaying, let lastTime = lastUpdateTime else { return }

        let now = Date()
        let deltaTime = now.timeIntervalSince(lastTime)

        // 更新本地累计时间
        localAccumulatedTime += deltaTime
        lastUpdateTime = now

        // 更新进度条显示
        updateProgressDisplay()

        // 检查是否完成任务周期（水桶满了）
        let currentTotalSeconds = globalTaskCurrentSeconds + Int(localAccumulatedTime)
        if hasGlobalTaskConfig && currentTotalSeconds >= globalTaskTotalSeconds {
            // 完成一个任务周期，触发金币奖励
            onGoldCoinEarned?()

            print("[GoldCoin] 完成任务周期，触发金币奖励")

            // 注意：这里不重置进度，等待服务器更新配置
        }
    }

    /// 更新进度条显示
    private func updateProgressDisplay() {
        guard isLoggedIn else { return }

        progressLayer.isHidden = false

        if hasGlobalTaskConfig {
            // 计算当前总进度：服务器进度 + 本地累计时间
            let currentTotalSeconds = globalTaskCurrentSeconds + Int(localAccumulatedTime)
            let progress = globalTaskTotalSeconds > 0 ? Double(currentTotalSeconds) / Double(globalTaskTotalSeconds) : 0.0

            progressLayer.strokeEnd = CGFloat(min(progress, 1.0))
            print("[GoldCoin] 更新任务进度: \(Int(progress * 100))%，\(currentTotalSeconds)/\(globalTaskTotalSeconds)秒")
        } else {
            // 没有配置时显示转圈动画
            progressLayer.strokeEnd = 0
            print("[GoldCoin] 等待任务配置加载...")
        }
    }

    /// 更新本地累计时间
    private func updateLocalAccumulatedTime() {
        guard let lastTime = lastUpdateTime else { return }

        let now = Date()
        let deltaTime = now.timeIntervalSince(lastTime)

        localAccumulatedTime += deltaTime
        lastUpdateTime = now

        // 更新进度条显示
        updateProgressDisplay()
    }

    /// 设置是否显示进度条（用于首页等无动画场景）
    func setProgressVisible(_ visible: Bool) {
        shouldShowProgress = visible
        if !visible {
            stopGoldCoinTimer()
            progressLayer.isHidden = true
        }
    }

    /// 设置金币图片
    func setCoinImage(_ image: UIImage?) {
        coinImageView.image = image
    }

    /// 获取当前进度百分比（用于调试）
    func getCurrentProgress() -> Double {
        return globalTaskProgress
    }

    /// 手动添加观看时间（用于测试）
    func addTestWatchTime(_ seconds: TimeInterval) {
        guard isLoggedIn else { return }

        localAccumulatedTime += seconds
        updateProgressDisplay()

        // 检查是否满足任务条件
        let currentTotalSeconds = globalTaskCurrentSeconds + Int(localAccumulatedTime)
        if hasGlobalTaskConfig && currentTotalSeconds >= globalTaskTotalSeconds {
            onGoldCoinEarned?()
        }

        print("[GoldCoin] 测试添加 \(seconds)s，当前本地累计时间: \(localAccumulatedTime)s")
    }

    /// 停止所有动画和计时器
    func stopAll() {
        stopGoldCoinTimer()
        progressLayer.removeAllAnimations()
        isAnimating = false
        isPlaying = false

        if isLoggedIn {
            updateLocalAccumulatedTime()
        }
    }

    // MARK: - Global Task Methods

    /// 更新全局任务配置
    /// - Parameters:
    ///   - totalSeconds: 任务总需要秒数（conditionValue）
    ///   - currentSeconds: 当前已完成秒数（viewingSeconds）
    func updateGlobalTaskConfig(totalSeconds: Int, currentSeconds: Int) {
        self.globalTaskTotalSeconds = totalSeconds
        self.globalTaskCurrentSeconds = currentSeconds
        self.hasGlobalTaskConfig = totalSeconds > 0

        // 重置本地累计时间，因为服务器进度是权威数据
        self.localAccumulatedTime = 0

        // 计算进度
        self.globalTaskProgress = totalSeconds > 0 ? Double(currentSeconds) / Double(totalSeconds) : 0.0

        // 更新显示
        updateProgressDisplay()

        print("[GoldCoin] 更新全局任务配置: \(currentSeconds)/\(totalSeconds)秒 (\(Int(globalTaskProgress * 100))%)")
    }

    /// 获取当前本地累计时间（用于上报）
    func getCurrentLocalAccumulatedTime() -> TimeInterval {
        return localAccumulatedTime
    }

    /// 重置本地累计时间（上报成功后调用）
    func resetLocalAccumulatedTime() {
        localAccumulatedTime = 0
        print("[GoldCoin] 重置本地累计时间")
    }

    /// 更新任务进度显示
    private func updateTaskProgressDisplay() {
        // 直接调用统一的进度更新方法
        updateProgressDisplay()

        // 如果进度达到100%，可以添加完成动画
        if globalTaskProgress >= 1.0 {
            animateCompletion()
        }
    }

    /// 播放完成动画
    private func animateCompletion() {
        // 进度条闪烁效果
        let flashAnimation = CABasicAnimation(keyPath: "opacity")
        flashAnimation.fromValue = 1.0
        flashAnimation.toValue = 0.3
        flashAnimation.duration = 0.3
        flashAnimation.autoreverses = true
        flashAnimation.repeatCount = 3

        progressLayer.add(flashAnimation, forKey: "completionFlash")
    }

    /// 获取当前全局任务信息
    func getCurrentGlobalTaskInfo() -> (progress: Double, serverSeconds: Int, localSeconds: Int, totalSeconds: Int, hasConfig: Bool) {
        let currentTotalSeconds = globalTaskCurrentSeconds + Int(localAccumulatedTime)
        return (globalTaskProgress, globalTaskCurrentSeconds, Int(localAccumulatedTime), globalTaskTotalSeconds, hasGlobalTaskConfig)
    }

    /// 获取当前全局观看时间（兼容旧接口）
    func getCurrentGlobalWatchTime() -> TimeInterval {
        return TimeInterval(globalTaskCurrentSeconds) + localAccumulatedTime
    }

    /// 获取当前播放状态
    var isCurrentlyPlaying: Bool {
        return isPlaying
    }

    /// 设置视频已完整观看状态（停止计时但保持显示）
    func setVideoCompleted() {
        isPlaying = false
        stopGoldCoinTimer()
        print("[GoldCoin] 视频已完整观看，停止计时")
    }
}

// MARK: - CAAnimationDelegate
extension GoldCoinView: CAAnimationDelegate {
    func animationDidStop(_ anim: CAAnimation, finished flag: Bool) {
        if flag {
            isAnimating = false
            if !isLoggedIn {
                // 未登录用户：动画完成后保持进度条填满状态
                progressLayer.strokeEnd = 1
            }
        }
    }
}
