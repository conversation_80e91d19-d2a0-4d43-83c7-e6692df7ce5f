import SmartCodable

// MARK: - 活跃度进度查询响应模型

/// 活跃度奖励配置模型
struct ActiveRewardConfig: SmartCodable {
    var createBy: String?
    var updateBy: String?
    var createTime: String?
    var updateTime: String?
    var id: Int = 0
    var conditionType: Int = 0
    var conditionDesc: String?
    var conditionValue: Int = 0
    var rewardValue: Int = 0
    var isFixed: Bool = false
    var deleted: Bool = false
    var viewingSeconds: Int?
    var activitySeconds: Int?
}

/// 活跃度进度数据模型
struct ActiveProgressData: SmartCodable {
    /// 总活跃时长（秒）
    var totalSeconds: Int = 0
    /// 可领取的奖励列表
    var availableRewards: [ActiveRewardConfig]?
    /// 下一个短视频奖励配置
    var nextShortVideoRewardConfig: ActiveRewardConfig?
    /// 未领取的活跃奖励
    var unclaimedActiveRewards: [ActiveRewardConfig]?
}

/// 活跃度进度查询API响应
struct ActiveProgressResponse: SmartCodable {
    var status: Int = 0
    var errMsg: String = ""
    var msg: String = ""
    var data: ActiveProgressData?
    
    var isSuccess: Bool {
        return status == 200
    }
    
    var displayMessage: String {
        if !msg.isEmpty {
            return msg
        }
        if !errMsg.isEmpty {
            return errMsg
        }
        return "未知错误"
    }
}

// MARK: - 活跃度进度上报响应模型

/// 活跃度进度上报API响应
struct ReportActiveProgressResponse: SmartCodable {
    var status: Int = 0
    var errMsg: String = ""
    var msg: String = ""
    var data: ActiveProgressData?
    
    var isSuccess: Bool {
        return status == 200
    }
    
    var displayMessage: String {
        if !msg.isEmpty {
            return msg
        }
        if !errMsg.isEmpty {
            return errMsg
        }
        return "未知错误"
    }
}

// MARK: - 领取活跃金币响应模型

/// 领取活跃金币API响应
struct ClaimActiveRewardResponse: SmartCodable {
    var status: Int = 0
    var errMsg: String = ""
    var msg: String = ""
    var data: ActiveProgressData?
    
    var isSuccess: Bool {
        return status == 200
    }
    
    var displayMessage: String {
        if !msg.isEmpty {
            return msg
        }
        if !errMsg.isEmpty {
            return errMsg
        }
        return "未知错误"
    }
}

// MARK: - 获取下一个活跃任务奖励响应模型

/// 下一个活跃任务奖励API响应
struct NextActivityRewardResponse: SmartCodable {
    var status: Int = 0
    var errMsg: String = ""
    var msg: String = ""
    var data: ActiveRewardConfig?
    
    var isSuccess: Bool {
        return status == 200
    }
    
    var displayMessage: String {
        if !msg.isEmpty {
            return msg
        }
        if !errMsg.isEmpty {
            return errMsg
        }
        return "未知错误"
    }
}

// MARK: - 活跃度进度上报请求模型

/// 活跃度进度上报请求
struct ReportActiveProgressRequest: SmartCodable {
    /// 活跃时长（秒）
    var activeSeconds: Int = 0
}
