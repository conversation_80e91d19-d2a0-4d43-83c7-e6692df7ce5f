import Foundation
import UIKit

/// 活跃度进度管理器
/// 负责管理用户活跃度的上报和状态跟踪
class ActivityProgressManager {
    
    // MARK: - 单例
    static let shared = ActivityProgressManager()
    private init() {
        setupNotifications()
    }
    
    // MARK: - 属性
    
    /// 是否正在跟踪活跃度
    private var isTracking: Bool = false

    /// 是否已经初始化过（只有第一次进入任务中心时才启动）
    private var hasInitialized: Bool = false

    /// 活跃度开始时间（用于实时计算总活跃时长）
    private var activityStartTime: Date?

    /// 服务器返回的基础活跃时长（秒）
    private var baseActiveSeconds: Int = 0

    /// 上次上报时间
    private var lastReportTime: Date?

    /// 累计活跃时长（秒）
    private var accumulatedSeconds: Int = 0

    /// 上报间隔（秒）- 每30秒上报一次
    private let reportInterval: TimeInterval = 30

    /// 定时器
    private var reportTimer: Timer?

    /// 当前活跃度数据
    private var currentActiveData: ActiveProgressData?
    
    // MARK: - 通知名称
    static let activityProgressUpdatedNotification = Notification.Name("ActivityProgressUpdated")
    
    // MARK: - 公开方法
    
    /// 初始化并开始跟踪活跃度（只有第一次进入任务中心时调用）
    func initializeAndStartTracking() {
        guard !hasInitialized else {
            print("[ActivityProgress] 已经初始化过，跳过重复初始化")
            return
        }

        hasInitialized = true
        startTracking()
        print("[ActivityProgress] 首次初始化并开始跟踪活跃度")
    }

    /// 开始跟踪活跃度（内部方法）
    private func startTracking() {
        guard !isTracking else { return }

        isTracking = true
        activityStartTime = Date()
        lastReportTime = Date()
        accumulatedSeconds = 0

        // 启动定时器，每30秒上报一次
        reportTimer = Timer.scheduledTimer(withTimeInterval: reportInterval, repeats: true) { [weak self] _ in
            self?.reportAccumulatedProgress()
        }

        print("[ActivityProgress] 开始跟踪活跃度")
    }
    
    /// 暂停跟踪活跃度（应用进入后台时）
    func pauseTracking() {
        guard isTracking else { return }

        // 停止定时器但保持跟踪状态
        reportTimer?.invalidate()
        reportTimer = nil

        // 上报剩余的活跃时长
        if accumulatedSeconds > 0 {
            reportAccumulatedProgress()
        }

        print("[ActivityProgress] 暂停跟踪活跃度")
    }

    /// 恢复跟踪活跃度（应用进入前台时）
    func resumeTracking() {
        guard hasInitialized && !reportTimer?.isValid == true else { return }

        // 重新启动定时器
        lastReportTime = Date()
        reportTimer = Timer.scheduledTimer(withTimeInterval: reportInterval, repeats: true) { [weak self] _ in
            self?.reportAccumulatedProgress()
        }

        print("[ActivityProgress] 恢复跟踪活跃度")
    }

    /// 完全停止跟踪活跃度（仅用于清理）
    private func stopTracking() {
        isTracking = false
        reportTimer?.invalidate()
        reportTimer = nil
        activityStartTime = nil

        print("[ActivityProgress] 完全停止跟踪活跃度")
    }
    
    /// 手动上报活跃度（用于特殊场景，如首次激活）
    /// - Parameter seconds: 要上报的秒数
    func reportProgress(seconds: Int) {
        APIManager.shared.reportActiveProgress(activeSeconds: seconds) { [weak self] result in
            DispatchQueue.main.async {
                switch result {
                case .success(let response):
                    if response.isSuccess, let data = response.data {
                        self?.currentActiveData = data
                        self?.notifyProgressUpdated()
                        print("[ActivityProgress] 手动上报成功: \(seconds)秒")
                    } else {
                        print("[ActivityProgress] 手动上报失败: \(response.displayMessage)")
                    }
                case .failure(let error):
                    print("[ActivityProgress] 手动上报请求失败: \(error.localizedDescription)")
                }
            }
        }
    }
    
    /// 获取当前活跃度数据
    func getCurrentActiveData() -> ActiveProgressData? {
        return currentActiveData
    }

    /// 更新当前活跃度数据（用于领取奖励后同步状态）
    func updateCurrentActiveData(_ data: ActiveProgressData) {
        currentActiveData = data
        // 更新基础活跃时长
        baseActiveSeconds = data.totalSeconds
        // 重置活跃度开始时间
        activityStartTime = Date()
        notifyProgressUpdated()
        print("[ActivityProgress] 活跃度数据已手动更新，基础时长: \(baseActiveSeconds)秒")
    }

    /// 获取当前实时总活跃时长（基础时长 + 本次会话时长）
    func getCurrentTotalActiveSeconds() -> Int {
        let sessionSeconds = getCurrentSessionActiveSeconds()
        return baseActiveSeconds + sessionSeconds
    }

    /// 获取当前会话的活跃时长
    private func getCurrentSessionActiveSeconds() -> Int {
        guard let startTime = activityStartTime else { return 0 }
        let sessionDuration = Date().timeIntervalSince(startTime)
        return Int(sessionDuration)
    }
    
    /// 刷新活跃度数据
    func refreshActiveData() {
        APIManager.shared.queryActiveProgress { [weak self] result in
            DispatchQueue.main.async {
                switch result {
                case .success(let response):
                    if response.isSuccess, let data = response.data {
                        self?.currentActiveData = data
                        // 更新基础活跃时长
                        self?.baseActiveSeconds = data.totalSeconds
                        // 重置活跃度开始时间
                        self?.activityStartTime = Date()
                        self?.notifyProgressUpdated()
                        print("[ActivityProgress] 刷新活跃度数据成功，基础时长: \(data.totalSeconds)秒")
                    } else {
                        print("[ActivityProgress] 刷新活跃度数据失败: \(response.displayMessage)")
                    }
                case .failure(let error):
                    print("[ActivityProgress] 刷新活跃度数据请求失败: \(error.localizedDescription)")
                }
            }
        }
    }
    
    // MARK: - 私有方法
    
    /// 设置通知监听
    private func setupNotifications() {
        // 监听应用进入前台和后台
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(appDidBecomeActive),
            name: UIApplication.didBecomeActiveNotification,
            object: nil
        )
        
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(appWillResignActive),
            name: UIApplication.willResignActiveNotification,
            object: nil
        )
    }
    
    /// 上报累计的活跃进度
    private func reportAccumulatedProgress() {
        guard isTracking else { return }
        
        // 计算自上次上报以来的活跃时长
        let now = Date()
        if let lastTime = lastReportTime {
            let interval = now.timeIntervalSince(lastTime)
            accumulatedSeconds += Int(interval)
        }
        
        // 如果累计时长大于0，则上报
        if accumulatedSeconds > 0 {
            let secondsToReport = accumulatedSeconds
            accumulatedSeconds = 0 // 重置累计时长
            
            APIManager.shared.reportActiveProgress(activeSeconds: secondsToReport) { [weak self] result in
                DispatchQueue.main.async {
                    switch result {
                    case .success(let response):
                        if response.isSuccess, let data = response.data {
                            self?.currentActiveData = data
                            self?.notifyProgressUpdated()
                            print("[ActivityProgress] 自动上报成功: \(secondsToReport)秒")
                        } else {
                            print("[ActivityProgress] 自动上报失败: \(response.displayMessage)")
                        }
                    case .failure(let error):
                        print("[ActivityProgress] 自动上报请求失败: \(error.localizedDescription)")
                    }
                }
            }
        }
        
        lastReportTime = now
    }
    
    /// 发送活跃度更新通知
    private func notifyProgressUpdated() {
        NotificationCenter.default.post(
            name: ActivityProgressManager.activityProgressUpdatedNotification,
            object: self,
            userInfo: ["data": currentActiveData as Any]
        )
    }
    
    // MARK: - 通知处理
    
    @objc private func appDidBecomeActive() {
        // 应用进入前台时恢复跟踪（如果已经初始化过）
        resumeTracking()
    }

    @objc private func appWillResignActive() {
        // 应用进入后台时暂停跟踪
        pauseTracking()
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self)
        reportTimer?.invalidate()
    }
}

// MARK: - 扩展：便捷方法

extension ActivityProgressManager {
    
    /// 检查是否有可领取的奖励
    func hasAvailableRewards() -> Bool {
        guard let data = currentActiveData else { return false }

        // 获取实时总活跃时长
        let currentTotalSeconds = getCurrentTotalActiveSeconds()

        // 检查当前未领取的奖励（当前宝箱）
        if let unclaimedReward = data.unclaimedActiveRewards {
            // 检查是否满足领取条件
            let remainingSeconds = unclaimedReward.conditionValue - currentTotalSeconds
            return remainingSeconds <= 0
        }

        return false
    }
    
    /// 获取当前宝箱的剩余时间（秒）- 使用实时计算
    func getCurrentRewardRemainingSeconds() -> Int? {
        guard let data = currentActiveData else { return nil }

        // 获取实时总活跃时长
        let currentTotalSeconds = getCurrentTotalActiveSeconds()

        // 优先检查当前未领取的奖励
        if let unclaimedReward = data.unclaimedActiveRewards {
            let remainingSeconds = unclaimedReward.conditionValue - currentTotalSeconds
            return max(0, remainingSeconds)
        }

        // 如果没有当前宝箱，检查下一个奖励
        if let nextReward = data.nextShortVideoRewardConfig {
            let remainingSeconds = nextReward.conditionValue - currentTotalSeconds
            return max(0, remainingSeconds)
        }

        return nil
    }
    
    /// 获取当前可领取的奖励金币数
    func getCurrentAvailableReward() -> Int {
        guard let data = currentActiveData else { return 0 }

        // 检查当前未领取的奖励
        if let unclaimedReward = data.unclaimedActiveRewards {
            let remainingSeconds = unclaimedReward.conditionValue - data.totalSeconds
            if remainingSeconds <= 0 {
                return unclaimedReward.rewardValue
            }
        }

        return 0
    }

    /// 获取当前宝箱的奖励配置（无论是否可领取）
    func getCurrentRewardConfig() -> ActiveRewardConfig? {
        guard let data = currentActiveData else { return nil }

        // 优先返回当前未领取的奖励
        if let unclaimedReward = data.unclaimedActiveRewards {
            return unclaimedReward
        }

        // 如果没有当前宝箱，返回下一个奖励配置
        return data.nextShortVideoRewardConfig
    }
}
