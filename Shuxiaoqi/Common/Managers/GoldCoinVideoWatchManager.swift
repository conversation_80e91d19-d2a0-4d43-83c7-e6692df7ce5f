//
//  GoldCoinVideoWatchManager.swift
//  Shuxia<PERSON>qi
//
//  Created by Augment Agent on 2025/9/5.
//

import Foundation

/// 单视频观看进度管理器
/// 负责管理单个视频的观看时长记录、上报和奖励处理
/// 与全局水桶机制并存，互不干扰
class GoldCoinVideoWatchManager {
    
    // MARK: - Singleton
    static let shared = GoldCoinVideoWatchManager()
    private init() {}
    
    // MARK: - Properties
    
    /// 当前正在观看的视频ID
    private var currentVideoId: String?
    
    /// 当前视频累计观看时长（秒）
    private var accumulatedWatchTime: TimeInterval = 0

    /// 已上报的观看时长（秒）
    private var reportedWatchTime: TimeInterval = 0

    /// 上次记录时间点
    private var lastRecordTime: Date?
    
    /// 是否正在观看
    private var isWatching: Bool = false
    
    /// 上报定时器
    private var reportTimer: Timer?
    
    /// 当前视频的任务配置
    private var currentVideoConfig: GoldCoinTaskWatchVideoRewardConfigData?
    
    /// 当前视频进度信息
    private var currentVideoProgress: GoldCoinVideoProgressData?
    
    /// 是否已完成当前视频任务
    private var isCurrentVideoCompleted: Bool = false
    
    // MARK: - Callbacks
    
    /// 进度更新回调 (进度百分比, 当前秒数, 总需要秒数)
    var onProgressUpdate: ((Double, Int, Int) -> Void)?
    
    /// 获得奖励回调 (奖励金币数量, 奖励信息)
    var onRewardEarned: ((Int, String) -> Void)?
    
    /// 任务完成回调 (完成消息)
    var onTaskCompleted: ((String) -> Void)?
    
    /// 错误回调 (错误信息) - 仅用于调试，不影响用户体验
    var onError: ((String) -> Void)?
    
    // MARK: - Public Methods
    
    /// 开始观看视频
    /// - Parameters:
    ///   - videoId: 视频ID
    ///   - isNoteType: 是否为笔记类型作品
    func startWatching(videoId: String, isNoteType: Bool = false) {
        print("[GoldCoinVideoWatch] 开始观看视频: \(videoId), 是否笔记: \(isNoteType)")
        
        // 如果是笔记类型，只显示UI不进行实际计时
        if isNoteType {
            print("[GoldCoinVideoWatch] 笔记类型作品，仅显示UI")
            // 笔记类型不触发任何进度更新，保持全局任务配置不变
            return
        }
        
        // 如果正在观看其他视频，先停止
        if isWatching && currentVideoId != videoId {
            stopWatching()
        }
        
        // 设置新的观看状态
        currentVideoId = videoId
        isWatching = true
        lastRecordTime = Date()
        accumulatedWatchTime = 0
        reportedWatchTime = 0
        isCurrentVideoCompleted = false
        
        // 获取视频任务配置和当前进度
        loadVideoConfigAndProgress()
        
        // 启动计时器（每5秒上报一次）
        startReportTimer()
    }
    
    /// 暂停观看
    func pauseWatching() {
        guard isWatching else { return }
        
        print("[GoldCoinVideoWatch] 暂停观看")
        
        // 更新累计时长
        updateAccumulatedTime()
        
        // 停止计时器
        stopReportTimer()
        
        // 立即上报当前进度
        reportProgress()
        
        isWatching = false
    }
    
    /// 恢复观看
    func resumeWatching() {
        guard currentVideoId != nil, !isWatching, !isCurrentVideoCompleted else { return }
        
        print("[GoldCoinVideoWatch] 恢复观看")
        
        isWatching = true
        lastRecordTime = Date()
        
        // 重新启动计时器
        startReportTimer()
    }
    
    /// 停止观看（切换视频或退出页面时调用）
    func stopWatching() {
        guard isWatching else { return }
        
        print("[GoldCoinVideoWatch] 停止观看")
        
        // 更新累计时长
        updateAccumulatedTime()
        
        // 停止计时器
        stopReportTimer()
        
        // 最后一次上报
        reportProgress()
        
        // 重置状态
        resetState()
    }
    
    /// 拖拽进度条（不计入有效时长）
    func seekTo(time: TimeInterval) {
        guard isWatching else { return }
        
        print("[GoldCoinVideoWatch] 拖拽进度条，重新开始计时")
        
        // 重新开始计时，不累加拖拽前的时间
        lastRecordTime = Date()
    }
    
    // MARK: - Private Methods
    
    /// 加载视频配置和进度
    private func loadVideoConfigAndProgress() {
        guard let videoId = currentVideoId else { return }
        
        // 获取任务配置
        APIManager.shared.getTaskWatchVideoRewardConfig { [weak self] result in
            DispatchQueue.main.async {
                switch result {
                case .success(let response):
                    if response.isSuccess {
                        self?.currentVideoConfig = response.data
                        print("[GoldCoinVideoWatch] 获取任务配置成功: \(response.data?.conditionValue ?? 0)秒")
                    }
                case .failure(let error):
                    print("[GoldCoinVideoWatch] 获取任务配置失败: \(error)")
                    self?.onError?("获取任务配置失败")
                }
            }
        }
        
        // 获取当前视频进度
        APIManager.shared.getVideoWatchProgress(videoId: videoId) { [weak self] result in
            DispatchQueue.main.async {
                switch result {
                case .success(let response):
                    // 检查status=500，表示视频已完整观看过
                    if response.status == 500 {
                        print("[GoldCoinVideoWatch] 视频已完整观看过，停止计时")
                        self?.isCurrentVideoCompleted = true
                        self?.onTaskCompleted?("视频已完整观看过")
                        self?.stopWatching()
                        return
                    }

                    if response.isSuccess {
                        self?.currentVideoProgress = response.data
                        let currentProgress = TimeInterval(response.data?.currentProgress ?? 0)
                        self?.accumulatedWatchTime = currentProgress
                        self?.reportedWatchTime = currentProgress // 已有进度视为已上报
                        self?.isCurrentVideoCompleted = response.data?.completed ?? false

                        print("[GoldCoinVideoWatch] 获取视频进度成功: \(response.data?.currentProgress ?? 0)/\(response.data?.totalDuration ?? 0)秒")

                        // 更新UI显示
                        self?.updateProgressUI()

                        // 如果已完成，不再计时
                        if self?.isCurrentVideoCompleted == true {
                            self?.onTaskCompleted?("视频观看任务已完成")
                            self?.stopWatching()
                        }
                    }
                case .failure(let error):
                    print("[GoldCoinVideoWatch] 获取视频进度失败: \(error)")
                    self?.onError?("获取视频进度失败")
                }
            }
        }
    }
    
    /// 启动上报定时器
    private func startReportTimer() {
        stopReportTimer()
        
        reportTimer = Timer.scheduledTimer(withTimeInterval: 5.0, repeats: true) { [weak self] _ in
            self?.updateAccumulatedTime()
            self?.reportProgress()
        }
    }
    
    /// 停止上报定时器
    private func stopReportTimer() {
        reportTimer?.invalidate()
        reportTimer = nil
    }
    
    /// 更新累计观看时长
    private func updateAccumulatedTime() {
        guard let lastTime = lastRecordTime else { return }

        let now = Date()
        let deltaTime = now.timeIntervalSince(lastTime)

        // 只有在正常播放状态下才累加时间
        if isWatching && deltaTime > 0 && deltaTime < 10 { // 防止异常大的时间差
            accumulatedWatchTime += deltaTime
            print("[GoldCoinVideoWatch] 更新累计时长: +\(deltaTime)s, 总计: \(accumulatedWatchTime)s")

            // 实时更新UI进度
            updateProgressUI()
        }

        lastRecordTime = now
    }
    
    /// 上报观看进度
    private func reportProgress() {
        guard let videoId = currentVideoId, accumulatedWatchTime > reportedWatchTime else { return }

        // 计算本次需要上报的增量时长
        let incrementalSeconds = Int(accumulatedWatchTime - reportedWatchTime)

        // 如果增量小于1秒，不上报
        guard incrementalSeconds > 0 else { return }

        print("[GoldCoinVideoWatch] 上报观看进度增量: \(incrementalSeconds)秒 (累计: \(Int(accumulatedWatchTime))秒)")

        APIManager.shared.reportVideoWatchProgress(videoId: videoId, watchSeconds: incrementalSeconds) { [weak self] result in
            DispatchQueue.main.async {
                self?.handleReportResponse(result, reportedIncrement: incrementalSeconds)
            }
        }
    }
    
    /// 处理上报响应
    private func handleReportResponse(_ result: Result<GoldCoinWatchVideoResponse, APIError>, reportedIncrement: Int) {
        switch result {
        case .success(let response):
            handleSuccessResponse(response, reportedIncrement: reportedIncrement)
        case .failure(let error):
            print("[GoldCoinVideoWatch] 上报失败: \(error)")
            // 静默处理错误，不影响用户体验
            onError?("上报失败: \(error.localizedDescription)")
        }
    }
    
    /// 处理成功响应
    private func handleSuccessResponse(_ response: GoldCoinWatchVideoResponse, reportedIncrement: Int) {
        if response.status == 500 {
            // 错误状态：视频已完整观看过或其他错误
            print("[GoldCoinVideoWatch] 服务器返回错误: \(response.msg)")

            // 标记为已完成，停止继续计时和上报
            isCurrentVideoCompleted = true
            onTaskCompleted?(response.msg)

            // 停止计时器和观看
            stopWatching()

            onError?(response.msg)
            return
        }

        guard response.isSuccess, let data = response.data else {
            print("[GoldCoinVideoWatch] 响应数据异常")
            onError?("响应数据异常")
            return
        }

        print("[GoldCoinVideoWatch] 上报成功: \(data.message)")

        // 上报成功后，更新已上报时长
        reportedWatchTime += TimeInterval(reportedIncrement)

        // 使用服务器返回的总时长作为权威数据
        accumulatedWatchTime = TimeInterval(data.totalSeconds)

        // 检查是否完成任务
        if data.videoCompleted {
            isCurrentVideoCompleted = true
            onTaskCompleted?(data.message)

            // 检查是否有奖励
            if !data.claimedRewards.isEmpty {
                let totalReward = data.claimedRewards.reduce(0) { $0 + $1.rewardValue }
                onRewardEarned?(totalReward, data.message)
            }

            // 停止计时
            stopWatching()
        } else {
            // 继续观看，更新UI
            updateProgressUI()
        }
    }
    
    /// 更新进度UI
    private func updateProgressUI() {
        // 这里不更新UI，因为全局任务进度由GoldCoinView自己管理
        // 只在上报成功时通过onProgressUpdate回调更新
        print("[GoldCoinVideoWatch] 本地累计时长: \(accumulatedWatchTime)s")
    }
    
    /// 重置状态
    private func resetState() {
        currentVideoId = nil
        accumulatedWatchTime = 0
        reportedWatchTime = 0
        lastRecordTime = nil
        isWatching = false
        currentVideoConfig = nil
        currentVideoProgress = nil
        isCurrentVideoCompleted = false
    }
    
    // MARK: - Public Getters
    
    /// 获取当前观看状态
    var isCurrentlyWatching: Bool {
        return isWatching
    }
    
    /// 获取当前视频ID
    var currentWatchingVideoId: String? {
        return currentVideoId
    }
    
    /// 获取当前累计时长
    var currentAccumulatedTime: TimeInterval {
        return accumulatedWatchTime
    }

    // MARK: - Debug Methods

    /// 获取管理器状态信息（用于调试）
    func getDebugInfo() -> String {
        var info = "=== GoldCoinVideoWatchManager 状态 ===\n"
        info += "当前视频ID: \(currentVideoId ?? "无")\n"
        info += "观看状态: \(isWatching ? "观看中" : "未观看")\n"
        info += "累计时长: \(accumulatedWatchTime)秒\n"
        info += "任务完成: \(isCurrentVideoCompleted ? "是" : "否")\n"

        if let config = currentVideoConfig {
            info += "任务配置: \(config.conditionValue)秒可获得\(config.rewardValue)金币\n"
        } else {
            info += "任务配置: 未加载\n"
        }

        if let progress = currentVideoProgress {
            info += "服务器进度: \(progress.currentProgress)/\(progress.totalDuration)秒\n"
            info += "服务器完成状态: \(progress.completed ? "已完成" : "进行中")\n"
        } else {
            info += "服务器进度: 未加载\n"
        }

        return info
    }

    /// 强制重置状态（用于测试）
    func forceReset() {
        stopReportTimer()
        resetState()
        print("[GoldCoinVideoWatch] 强制重置状态完成")
    }
}
